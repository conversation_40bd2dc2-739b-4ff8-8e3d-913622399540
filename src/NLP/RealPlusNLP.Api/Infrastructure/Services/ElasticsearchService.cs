using Microsoft.Extensions.Options;
using Nest;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Documents;
using RealPlusNLP.Api.Common.Models;
using RealPlusNLP.Api.Configuration.Options;

namespace RealPlusNLP.Api.Infrastructure.Services;

public class ElasticsearchService(
    IElasticClient client,
    IOptions<ElasticsearchOptions> options)
    : IElasticsearchService
{
    private readonly IElasticClient _client = client;
    private readonly ElasticsearchOptions _options = options.Value;

    public async Task<IReadOnlyCollection<PropertySearchModel>> SearchAsync(
        string searchText,
        CancellationToken cancellationToken = default)
    {
        static QueryContainer Prefix(string searchParam, QueryContainerDescriptor<PropertyDocument> q)
        {
            return q.Prefix(p => p.Field(f => f.BuildingName).Value(searchParam));
        }

        static QueryContainer MatchPhrasePrefix(string searchParam, QueryContainerDescriptor<PropertyDocument> q)
        {
            return q.MatchPhrasePrefix(p => p.Field(f => f.BuildingName).Query(searchParam));
        }

        var response = await _client.SearchAsync<PropertyDocument>(s => s
            .Index($"resource-{_options.BuildingIndexName}")
                 .Source(ss => ss.Includes(o => o.Fields(f => f.BuildingName, f => f.RPBin)))
                 .Query(q => q.Bool(
                      b => b.Should(
                            should => MatchPhrasePrefix(searchText, should),
                            should => Prefix(searchText, should)
            ))).Size(1000), cancellationToken);

        return response.Documents.GroupBy(document => document.BuildingName.ToUpper())
            .Select(groupItem => new PropertySearchModel
            (
                groupItem.Key,
                [.. groupItem.Select(item => item.RPBin ?? 0).OrderBy(item => item)]
            )).Take(5).ToList().AsReadOnly();
    }
}
